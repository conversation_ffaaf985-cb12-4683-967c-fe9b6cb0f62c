﻿using System.Text;
using Newtonsoft.Json;
using Microsoft.CognitiveServices.Speech.Audio;
using Microsoft.CognitiveServices.Speech;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using DotNetEnv;
using Azure.Storage.Blobs;
using Microsoft.CognitiveServices.Speech.Transcription;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;
using Microsoft.AspNetCore.Components;
using Microsoft.Graph.Models;

namespace TeyaUIViewModels.ViewModel
{
    public class SpeechService : ISpeechService
    {
        private readonly HttpClient httpClient;
        private ConversationTranscriber ConversationTranscriber;
        private List<Speech> collectedSpeeches = new List<Speech>();
        private readonly ILogger<SpeechService> logger;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> localizer;
        private readonly ITokenService _tokenService;
        private Guid currentRecordingId;
        public IEnumerable<Speech>? Speech { get; set; }
        private List<WordTiming> tempWordTimings = new List<WordTiming>();
        public String TotalTranscribed { get; set; } = String.Empty;
        public String SpeechData { get; set; } = String.Empty;
        private const int zero = 0;
        private const int buffersize = 1024;
        private readonly ActiveUser User;
        private readonly string _EncounterNotes;
        private bool disposed = false;

        public SpeechService(HttpClient _httpClient, ILogger<SpeechService> _logger, IStringLocalizer<TeyaUIViewModelsStrings> _localizer, ITokenService tokenService, ActiveUser user)
        {
            httpClient = _httpClient ?? throw new ArgumentNullException(nameof(_httpClient));
            logger = _logger ?? throw new ArgumentNullException(nameof(_logger));
            localizer = _localizer ?? throw new ArgumentNullException(nameof(_localizer));
            Env.Load();
            _tokenService = tokenService;
            User = user;
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
        }
        public Guid GetCurrentRecordingId()
        {
            return currentRecordingId;
        }
        private PushAudioInputStream pushStream;

        // Add this method to receive audio from JavaScript
        public async Task ProcessAudioChunk(string base64AudioChunk)
        {
            if (pushStream != null)
            {
                try
                {
                    // Convert base64 string to byte array
                    byte[] audioBytes = Convert.FromBase64String(base64AudioChunk);
                    pushStream.Write(audioBytes);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error processing audio chunk");
                }
            }
        }
        public async Task StartTranscriptionAsync(Guid id)
        {
            currentRecordingId = id;
            collectedSpeeches.Clear();
            tempWordTimings.Clear();
            TotalTranscribed = string.Empty;
            SpeechData = string.Empty;
            var speechConfig = InitializeSpeechConfig();

            var pushStream = AudioInputStream.CreatePushStream(AudioStreamFormat.GetWaveFormatPCM(16000, 16, 1));
            var audioConfig = AudioConfig.FromStreamInput(pushStream);

            // Store the pushStream to access it later
            this.pushStream = pushStream;
            using var speechRecognizer = new SpeechRecognizer(speechConfig, audioConfig);
            speechConfig.SetProperty(PropertyId.SpeechServiceResponse_DiarizeIntermediateResults, localizer["true"]);

            ConversationTranscriber = new ConversationTranscriber(speechConfig, audioConfig);
            var stopRecognition = new TaskCompletionSource<int>(TaskCreationOptions.RunContinuationsAsynchronously);

            RegisterTranscriberEvents(stopRecognition);

            await StartTranscriptionInternalAsync(stopRecognition);
        }

        private SpeechConfig InitializeSpeechConfig()
        {
            var apiKey = Environment.GetEnvironmentVariable("AZURE-SPEECH-API-KEY");
            var region = Environment.GetEnvironmentVariable("AZURE-REGION");
            var language = Environment.GetEnvironmentVariable("AZURE-LANGUAGE");

            var speechConfig = SpeechConfig.FromSubscription(apiKey, region);
            speechConfig.SpeechRecognitionLanguage = language;

            return speechConfig;
        }




        private void RegisterTranscriberEvents(TaskCompletionSource<int> stopRecognition)
        {
            ConversationTranscriber.Transcribed += (s, e) =>
            {
                HandleTranscribedEvent(e);
            };

            ConversationTranscriber.Canceled += (s, e) =>
            {
                stopRecognition.TrySetResult(zero);
            };

            ConversationTranscriber.SessionStopped += async (s, e) =>
            {
                await HandleSessionStoppedEvent(stopRecognition);
            };
        }

        private void HandleTranscribedEvent(ConversationTranscriptionEventArgs e)
        {
            try
            {
                if (e.Result.Reason == ResultReason.RecognizedSpeech)
                {
                    TotalTranscribed += $"\n{e.Result.SpeakerId} : {e.Result.Text}";
                    SpeechData += e.Result.Text;
                    var recognizedText = e.Result.Text;
                    var offsetInTicks = e.Result.OffsetInTicks;
                    var durationInTicks = e.Result.Duration.Ticks;

                    var wordTiming = new WordTiming
                    {
                        Word = recognizedText,
                        StartTime = TimeSpan.FromTicks(offsetInTicks).TotalSeconds,
                        EndTime = TimeSpan.FromTicks(offsetInTicks + durationInTicks).TotalSeconds
                    };

                    tempWordTimings.Add(wordTiming);
                }
                else if (e.Result.Reason == ResultReason.NoMatch)
                {
                    logger.LogWarning(localizer["SpeechNotRecognized"]);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error handling Transcribed event");
            }
        }

        private async Task HandleSessionStoppedEvent(TaskCompletionSource<int> stopRecognition)
        {
            stopRecognition.TrySetResult(zero);
            collectedSpeeches.Add(new Speech { Result = SpeechData, Timestamps = tempWordTimings, TranscribedData = TotalTranscribed });
        }

        private async Task StartTranscriptionInternalAsync(TaskCompletionSource<int> stopRecognition)
        {
            await ConversationTranscriber.StartTranscribingAsync();
            await Task.WhenAny(stopRecognition.Task);
        }

        public async Task StopTranscriptionAsync(Guid Id, Guid patientId, String VisitType, Guid? OrgID, bool Subscription)
        {
            if (ConversationTranscriber != null)
            {
                await ConversationTranscriber.StopTranscribingAsync();
                await PostTranscriptionsAsync(Id, patientId, VisitType, OrgID, Subscription);
            }
        }

        public async Task PostTranscriptionsAsync(Guid Id, Guid patientId, String VisitType, Guid? OrgID, bool Subscription)
        {
            if (collectedSpeeches.Count > zero)
            {
                var speechapi = $"{_EncounterNotes}/Speech/{OrgID}/{Subscription}";
                var uri = new Uri(speechapi);

                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                SpeechRequest speeches = new SpeechRequest
                {
                    Id = Id,
                    OrganizationId = OrgID,
                    PCPId = Guid.Parse(User.id),
                    PatientId = patientId,
                    Speeches = collectedSpeeches,
                    VisitType = VisitType,
                    accessToken = accessToken
                };
                var bodyContent = System.Text.Json.JsonSerializer.Serialize(speeches);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Post, speechapi)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                var response = await httpClient.SendAsync(requestMessage); if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                    dynamic result = JsonConvert.DeserializeObject(responseBody);
                    currentRecordingId = result.firstRecordId;
                }
                collectedSpeeches.Clear();
            }
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    pushStream?.Dispose();
                    ConversationTranscriber?.Dispose();
                }
                disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}

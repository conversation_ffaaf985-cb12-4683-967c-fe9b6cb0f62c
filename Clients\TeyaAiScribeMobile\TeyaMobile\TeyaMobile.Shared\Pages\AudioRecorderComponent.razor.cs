using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Microsoft.Maui.Controls;
using System.Timers;
using TeyaMobile.Shared.Scripts;
using TeyaMobile.Shared.Services;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.ViewModel;

namespace TeyaMobile.Shared.Pages
{
    public partial class AudioRecorderComponent : ComponentBase, IDisposable
    {
        [Parameter]
        public Guid PatientId { get; set; }
        [SupplyParameterFromQuery(Name = "providerId")]
        public Guid ProviderId { get; set; }
        [SupplyParameterFromQuery(Name = "appointmentId")]
        public string AppointmentId { get; set; }
        [SupplyParameterFromQuery(Name = "visitType")]
        public string VisitType { get; set; }
        [SupplyParameterFromQuery(Name = "orgId")]
        public Guid? OrganizationId { get; set; }
        [SupplyParameterFromQuery(Name = "sub")]
        public bool Subscription { get; set; }

        [Inject] private IAudioRecorder? AudioRecorder { get; set; }
        [Inject] private ILogger<AudioRecorderComponent>? Logger { get; set; }
        [Inject] private IFormFactor FormFactor { get; set; }
        [Inject] private ISpeechService? SpeechService { get; set; }
        [Inject] private IAuthenticationService? AuthService { get; set; }
        [Inject] private IJSRuntime? JSRuntime { get; set; }
        [Inject] private NavigationManager Navigation { get; set; }

        // Platform detection properties using IFormFactor
        private string CurrentPlatform => FormFactor.GetFormFactor();
        private bool IsWeb => CurrentPlatform == "Web";
        private bool IsNativePlatform => CurrentPlatform == "Phone";

        private string LiveTranscriptionText = "";
        private ElementReference transcriptionEditor;

        // Component state
        private bool IsRecording = false;
        private bool IsPaused = false;
        private bool IsProcessing = false;
        private int RecordingDuration = 0;
        private string TranscriptionText = "";
        private Guid CurrentRecordingId = Guid.Empty;
        private bool HasCompletedRecording = false;
        // Platform-specific helpers
        private AudioRecorderInterop? _audioInterop;
        private System.Timers.Timer? _durationTimer;
        private System.Timers.Timer? _animationTimer;
        private Random _random = new Random();
        private DotNetObjectReference<AudioRecorderComponent>? _dotNetRef;
        private DateTime _recordingStartTime;
        private TimeSpan _totalPausedDuration = TimeSpan.Zero;
        private DateTime? _pauseStartTime;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                _dotNetRef = DotNetObjectReference.Create(this);
                Logger?.LogInformation($"Initializing AudioRecorderComponent on platform: {CurrentPlatform}");

                // Only register AudioRecorder events if it exists (native platforms)
                if (IsNativePlatform && AudioRecorder != null)
                {
                    AudioRecorder.RecordingStateChanged += OnRecordingStateChanged;
                    AudioRecorder.ErrorOccurred += OnErrorOccurred;
                    Logger?.LogInformation("AudioRecorder events registered for native platform (Phone)");
                }
                else if (IsNativePlatform && AudioRecorder == null)
                {
                    Logger?.LogWarning("AudioRecorder service not available on native platform (Phone)");
                }

                if (SpeechService != null)
                {
                    SpeechService.OnPartialTranscription += OnPartialTranscription;
                    SpeechService.OnFinalTranscription += OnFinalTranscription;
                    SpeechService.OnError += OnSpeechError;
                    Logger?.LogInformation("SpeechService events registered");
                }
                else
                {
                    Logger?.LogWarning("SpeechService not available");
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error during AudioRecorderComponent initialization");
                await HandleError($"Initialization failed: {ex.Message}");
            }
        }

        private void OnPartialTranscription(object? sender, string text)
        {
            LiveTranscriptionText = text;
            TranscriptionText += text + " ";
            InvokeAsync(StateHasChanged);
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender && IsWeb && JSRuntime != null)
            {
                try
                {
                    // App settings are now initialized by AppSettingsInitializer component in App.razor
                    _audioInterop = new AudioRecorderInterop(JSRuntime, this);
                    await _audioInterop.InitializeAsync();
                    Logger?.LogInformation("Web audio interop initialized");
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "Failed to initialize web audio interop");
                    await HandleError($"Web audio initialization failed: {ex.Message}");
                }
            }
        }

        private async Task StartRecording()
        {
            try
            {
                Logger?.LogInformation($"Starting recording on platform: {CurrentPlatform}");
                IsProcessing = true;
                StateHasChanged();

                CurrentRecordingId = Guid.NewGuid();
                TranscriptionText = "";
                _recordingStartTime = DateTime.Now;
                _totalPausedDuration = TimeSpan.Zero;
                _pauseStartTime = null;

                if (IsWeb)
                {
                    await StartWebRecording();
                }
                else if (IsNativePlatform)
                {
                    await StartNativeRecording();
                }

                StartTimers();
                IsRecording = true;
                IsPaused = false;
                IsProcessing = false;

                await StartSpeechRecognition();

                Logger?.LogInformation($"Recording started successfully with ID: {CurrentRecordingId} on {CurrentPlatform}");
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to start recording");
                IsProcessing = false;
                await HandleError($"Failed to start recording: {ex.Message}");
            }
        }

        private async Task StartWebRecording()
        {
            if (_audioInterop != null)
            {
                await _audioInterop.StartRecordAsync();
                Logger?.LogInformation("Web recording started");
            }
            else
            {
                throw new InvalidOperationException("Web audio interop not initialized");
            }
        }

        private async Task StartNativeRecording()
        {
            if (AudioRecorder != null)
            {
                AudioRecorder.SetNextRecordingId(CurrentRecordingId);
                await AudioRecorder.StartRecordingAsync();
                Logger?.LogInformation("Native recording started on Phone platform");
            }
            else
            {
                throw new InvalidOperationException("AudioRecorder service not available");
            }
        }

        private async Task StartSpeechRecognition()
        {
            if (SpeechService != null)
            {
                if (IsWeb)
                {
                    // For web, use the same pattern as TeyaWebApp - StartTranscriptionAsync with new GUID
                    await SpeechService.StartTranscriptionAsync(Guid.NewGuid());
                }
                else
                {
                    // For native platforms, use continuous recognition
                    await SpeechService.StartContinuousRecognitionAsync();
                }
                Logger?.LogInformation($"Speech recognition started on {CurrentPlatform}");
            }
        }

        private async Task PauseResumeRecording()
        {
            try
            {
                if (IsPaused)
                {
                    await ResumeRecording();
                }
                else
                {
                    await PauseRecording();
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to pause/resume recording");
                await HandleError($"Failed to pause/resume recording: {ex.Message}");
            }
        }

        private async Task PauseRecording()
        {
            Logger?.LogInformation($"Pausing recording on {CurrentPlatform}");
            _pauseStartTime = DateTime.Now;

            if (IsWeb && _audioInterop != null)
            {
                await _audioInterop.PauseRecordAsync();
            }
            else if (IsNativePlatform && AudioRecorder != null)
            {
                await AudioRecorder.PauseRecordingAsync();
            }

            if (SpeechService != null && !IsWeb)
            {
                await SpeechService.StopContinuousRecognitionAsync();
            }

            IsPaused = true;
            _durationTimer?.Stop();
            Logger?.LogInformation("Recording paused");
            StateHasChanged();
        }

        private async Task ResumeRecording()
        {
            Logger?.LogInformation($"Resuming recording on {CurrentPlatform}");

            if (_pauseStartTime.HasValue)
            {
                _totalPausedDuration += DateTime.Now - _pauseStartTime.Value;
                _pauseStartTime = null;
            }

            if (IsWeb && _audioInterop != null)
            {
                await _audioInterop.ResumeRecordAsync();
            }
            else if (IsNativePlatform && AudioRecorder != null)
            {
                await AudioRecorder.ResumeRecordingAsync();
            }

            if (SpeechService != null && !IsWeb)
            {
                await SpeechService.StartContinuousRecognitionAsync();
            }

            IsPaused = false;
            _durationTimer?.Start();
            Logger?.LogInformation("Recording resumed");
            StateHasChanged();
        }

        private async Task StopRecording()
        {
            try
            {
                Logger?.LogInformation($"Stopping recording on {CurrentPlatform}");
                IsProcessing = true;
                StateHasChanged();

                StopTimers();

                string filePath = "";

                if (IsWeb)
                {
                    await StopWebRecording();
                }
                else if (IsNativePlatform)
                {
                    filePath = await StopNativeRecording();
                }

                await ProcessRecordingCompletion(filePath);

                // Set states for post-recording
                IsRecording = false;
                IsPaused = false;
                RecordingDuration = 0;
                HasCompletedRecording = true; // Enable post-recording controls
                IsProcessing = false;

                // Clear live transcription but keep complete transcription
                LiveTranscriptionText = "";

                Logger?.LogInformation("Recording stopped successfully");
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to stop recording");
                IsProcessing = false;
                await HandleError($"Failed to stop recording: {ex.Message}");
            }
        }


        private async Task ResetAudioInterop()
        {
            if (IsWeb && _audioInterop != null)
            {
                try
                {
                    // Dispose current interop and recreate
                    _audioInterop.Dispose();
                    _audioInterop = new AudioRecorderInterop(JSRuntime, this);
                    await _audioInterop.InitializeAsync();
                }
                catch (Exception ex)
                {
                    Logger?.LogWarning(ex, "Failed to reset audio interop");
                }
            }
        }

        private async Task ResetSpeechService()
        {
            if (SpeechService != null)
            {
                try
                {
                    // Stop any ongoing recognition
                    if (IsWeb)
                    {
                        await SpeechService.StopTranscriptionAsync(CurrentRecordingId, ProviderId, PatientId, VisitType, OrganizationId, Subscription);
                    }
                    else
                    {
                        await SpeechService.StopContinuousRecognitionAsync();
                    }
                }
                catch (Exception ex)
                {
                    Logger?.LogWarning(ex, "Failed to reset speech service");
                }
            }
        }

        private async Task ReRecord()
        {
            try
            {
                Logger?.LogInformation("Re-recording initiated - resetting component state");

                // Stop any ongoing timers
                StopTimers();

                // Reset all recording states
                HasCompletedRecording = false;
                IsRecording = false;
                IsPaused = false;
                IsProcessing = false;
                RecordingDuration = 0;
                CurrentRecordingId = Guid.Empty;

                // Clear all transcription data
                TranscriptionText = "";
                LiveTranscriptionText = "";

                // Reset timing variables
                _totalPausedDuration = TimeSpan.Zero;
                _pauseStartTime = null;
                _recordingStartTime = default;

                // Clean up any existing audio interop state for web
                if (IsWeb && _audioInterop != null)
                {
                    try
                    {
                        await ResetAudioInterop();
                        Logger?.LogInformation("Web audio interop reset");
                    }
                    catch (Exception ex)
                    {
                        Logger?.LogWarning(ex, "Failed to reset web audio interop");
                    }
                }

                // Reset native audio recorder state if available
                if (IsNativePlatform && AudioRecorder != null)
                {
                    try
                    {
                        await ResetAudioInterop();
                        Logger?.LogInformation("Native audio recorder reset");
                    }
                    catch (Exception ex)
                    {
                        Logger?.LogWarning(ex, "Failed to reset native audio recorder");
                    }
                }

                // Reset speech service state
                if (SpeechService != null)
                {
                    try
                    {
                        await ResetSpeechService();
                        Logger?.LogInformation("Speech service reset");
                    }
                    catch (Exception ex)
                    {
                        Logger?.LogWarning(ex, "Failed to reset speech service");
                    }
                }

                Logger?.LogInformation("Re-record completed - component reset to initial state");
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to re-record");
                await HandleError($"Failed to re-record: {ex.Message}");
            }
        }

        private async Task ProcessRecording()
        {
            try
            {
                Logger?.LogInformation("Processing recording - navigating to SOAP notes");
                await InvokeAsync(() =>
                {
                    Navigation.NavigateTo($"/soapnotes/{PatientId}?orgId={OrganizationId}&sub={Subscription}");
                });
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to process recording");
                await HandleError($"Failed to process recording: {ex.Message}");
            }
        }

        private async Task StopWebRecording()
        {
            if (_audioInterop != null)
            {
                // For web, use the same pattern as TeyaWebApp - pass CurrentRecordingId and access token
                await _audioInterop.StopRecordAsync(CurrentRecordingId, await GetAccessTokenAsync());
                Logger?.LogInformation("Web recording stopped");
            }
        }

        private async Task<string> GetAccessTokenAsync()
        {
            // Get access token from authentication service
            if (AuthService != null)
            {
                try
                {
                    return await AuthService.GetAccessTokenAsync();
                }
                catch (Exception ex)
                {
                    Logger?.LogWarning(ex, "Failed to get access token");
                    return string.Empty;
                }
            }
            return string.Empty;
        }

        private async Task<string> StopNativeRecording()
        {
            string filePath = "";

            if (AudioRecorder != null)
            {
                filePath = await AudioRecorder.StopRecordingAsync();
                Logger?.LogInformation($"Native recording stopped on Phone platform. File path: {filePath}");
            }
            else
            {
                Logger?.LogWarning("AudioRecorder not available for stopping native recording");
            }

            return filePath;
        }

        private async Task ProcessRecordingCompletion(string filePath)
        {
            if (SpeechService == null)
            {
                Logger?.LogWarning("SpeechService not available for processing recording completion");
                return;
            }

            try
            {
                Logger?.LogInformation($"Processing recording completion on {CurrentPlatform}");

                if (IsWeb)
                {
                    // For web, stop transcription and let JavaScript handle upload
                    await SpeechService.StopTranscriptionAsync(
                        CurrentRecordingId,
                        ProviderId,
                        PatientId,
                        VisitType,
                        OrganizationId,
                        Subscription
                    );
                    Logger?.LogInformation("Web transcription stopped and processed");
                }
                else if (IsNativePlatform)
                {
                    await SpeechService.StopContinuousRecognitionAsync();

                    if (!string.IsNullOrEmpty(filePath) && System.IO.File.Exists(filePath))
                    {
                        Logger?.LogInformation($"Uploading audio file from Phone platform: {filePath}");
                        await SpeechService.UploadAudioToBackendAsync(filePath);
                        Logger?.LogInformation("Audio file uploaded successfully");

                        Logger?.LogInformation("Posting transcriptions to backend from Phone platform");
                        await SpeechService.PostTranscriptionsAsync(
                            CurrentRecordingId,
                            ProviderId,
                            PatientId,
                            VisitType,
                            OrganizationId,
                            Subscription
                        );
                        Logger?.LogInformation("Transcriptions posted successfully");
                    }
                    else
                    {
                        Logger?.LogWarning($"Audio file not found or empty path: {filePath}. Posting transcriptions without file.");
                        await SpeechService.PostTranscriptionsAsync(
                            CurrentRecordingId,
                            ProviderId,
                            PatientId,
                            VisitType,
                            OrganizationId,
                            Subscription
                        );
                    }
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to process recording completion");
                await HandleError($"Failed to process recording completion: {ex.Message}");
            }
        }

        private TimeSpan GetEffectiveRecordingDuration()
        {
            var totalDuration = DateTime.Now - _recordingStartTime;
            var effectiveDuration = totalDuration - _totalPausedDuration;

            if (_pauseStartTime.HasValue)
            {
                var currentPauseDuration = DateTime.Now - _pauseStartTime.Value;
                effectiveDuration -= currentPauseDuration;
            }

            return effectiveDuration;
        }

        private void StartTimers()
        {
            RecordingDuration = 0;
            _durationTimer = new System.Timers.Timer(1000);
            _durationTimer.Elapsed += OnDurationTimerElapsed;
            _durationTimer.Start();

            if (IsWeb)
            {
                _animationTimer = new System.Timers.Timer(100);
                _animationTimer.Elapsed += OnAnimationTimerElapsed;
                _animationTimer.Start();
            }
        }

        private void StopTimers()
        {
            _durationTimer?.Stop();
            _durationTimer?.Dispose();
            _durationTimer = null;

            _animationTimer?.Stop();
            _animationTimer?.Dispose();
            _animationTimer = null;
        }

        private void OnDurationTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            RecordingDuration++;
            InvokeAsync(StateHasChanged);
        }

        private void OnAnimationTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            InvokeAsync(StateHasChanged);
        }

        // Event handlers for native platforms
        private void OnRecordingStateChanged(object? sender, RecordingState state)
        {
            Logger?.LogInformation($"Recording state changed on {CurrentPlatform}: {state}");
            InvokeAsync(StateHasChanged);
        }

        private void OnErrorOccurred(object? sender, Exception ex)
        {
            Logger?.LogError(ex, $"AudioRecorder error occurred on {CurrentPlatform}");
            InvokeAsync(() => HandleError($"Recording error: {ex.Message}"));
        }

        private void OnFinalTranscription(object? sender, string text)
        {
            Logger?.LogInformation($"Final transcription received on {CurrentPlatform}: {text}");
        }

        private void OnSpeechError(object? sender, Exception ex)
        {
            Logger?.LogError(ex, $"Speech recognition error on {CurrentPlatform}");
            InvokeAsync(() => HandleError($"Speech recognition error: {ex.Message}"));
        }

        // JSInvokable methods for web platform
        [JSInvokable]
        public async Task ProcessAudioChunk(string base64AudioChunk)
        {
            if (SpeechService != null)
            {
                await SpeechService.ProcessAudioChunk(base64AudioChunk);
            }
        }

        [JSInvokable]
        public async Task OnRecordingComplete(string recordId)
        {
            Logger?.LogInformation($"Web recording completed: {recordId}");
        }

        [JSInvokable]
        public async Task OnAudioDetected(bool isDetected)
        {
            await InvokeAsync(StateHasChanged);
        }

        [JSInvokable]
        public async Task ProcessAudioChunk(string base64AudioChunk)
        {
            // For web platform, process audio chunks like TeyaWebApp
            if (IsWeb && SpeechService != null)
            {
                try
                {
                    await SpeechService.ProcessAudioChunk(base64AudioChunk);
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "Failed to process audio chunk");
                }
            }
        }

        [JSInvokable]
        public async Task OnRecordingComplete(string recordingId)
        {
            // Called when web recording upload is complete
            Logger?.LogInformation($"Web recording upload completed for ID: {recordingId}");
        }

        [JSInvokable]
        public async Task OnRecordingError(string errorMessage)
        {
            Logger?.LogError("Web recording error: {ErrorMessage}", errorMessage);
            await HandleError($"Recording error: {errorMessage}");
        }

        [JSInvokable]
        public async Task OnRecordingError(string error)
        {
            Logger?.LogError("Web recording error: {Error}", error);
            await HandleError($"Web recording error: {error}");
        }

        // Helper Methods
        private string GetStatusClass()
        {
            if (IsProcessing) return "status-processing";
            if (IsRecording && IsPaused) return "status-paused";
            if (IsRecording) return "status-recording";
            return "status-ready";
        }

        private string GetStatusText()
        {
            if (IsProcessing) return "Processing...";
            if (IsRecording && IsPaused) return "Paused";
            if (IsRecording) return "Recording...";
            return $"Teya AI Scribe";
        }

        private string GetWaveBarStyle(int index)
        {
            if (!IsRecording || IsPaused)
            {
                return "--height: 20px;";
            }

            var height = _random.Next(20, 80);
            return $"--height: {height}px;";
        }

        private string FormatDuration(int seconds)
        {
            var timeSpan = TimeSpan.FromSeconds(seconds);
            return timeSpan.ToString(@"mm\:ss");
        }

        private async Task HandleError(string message)
        {
            Logger?.LogError("AudioRecorder Error on {Platform}: {Message}", CurrentPlatform, message);
            Console.WriteLine($"Audio Recorder Error on {CurrentPlatform}: {message}");
            IsProcessing = false;
            StateHasChanged();
        }

        public void Dispose()
        {
            try
            {
                StopTimers();

                // Only unregister if AudioRecorder exists and we're on native platform
                if (IsNativePlatform && AudioRecorder != null)
                {
                    AudioRecorder.RecordingStateChanged -= OnRecordingStateChanged;
                    AudioRecorder.ErrorOccurred -= OnErrorOccurred;
                }

                if (SpeechService != null)
                {
                    SpeechService.OnPartialTranscription -= OnPartialTranscription;
                    SpeechService.OnFinalTranscription -= OnFinalTranscription;
                    SpeechService.OnError -= OnSpeechError;
                }

                _audioInterop?.Dispose();
                _dotNetRef?.Dispose();

                Logger?.LogInformation($"AudioRecorderComponent disposed successfully on {CurrentPlatform}");
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error during AudioRecorderComponent disposal");
            }
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace TeyaMobileModel.Model
{
    
        public class ActiveUser
        {
            public string id { get; set; }
            public string displayName { get; set; }
            public string givenName { get; set; }
            public string surname { get; set; }
            public string userType { get; set; }
            public string jobTitle { get; set; }
            public string companyName { get; set; }
            public string department { get; set; }
            public string officeLocation { get; set; }
            public string streetAddress { get; set; }
            public string city { get; set; }
            public string state { get; set; }
            public string postalCode { get; set; }
            public string country { get; set; }
            public string role { get; set; }
            public List<string> businessPhones { get; set; } = new List<string>();

            public string mobilePhone { get; set; }
            public string mail { get; set; }

            [JsonPropertyName("OrganizationName")]
            public string OrganizationName { get; set; }

            [JsonPropertyName("Address")]
            public string Address { get; set; }

            [JsonIgnore]
            public string oDataContext { get; set; }
        }

        public class ActiveUserConverter : JsonConverter<ActiveUser>
        {
            public override ActiveUser Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
            {
                // Create a new JSON document by reading the current token
                if (reader.TokenType != JsonTokenType.StartObject)
                    throw new JsonException("Expected start of object");

                using var document = JsonDocument.ParseValue(ref reader);
                var root = document.RootElement;

                var user = new ActiveUser
                {
                    id = GetPropertyValue(root, "id"),
                    displayName = GetPropertyValue(root, "displayName"),
                    givenName = GetPropertyValue(root, "givenName"),
                    surname = GetPropertyValue(root, "surname"),
                    userType = GetPropertyValue(root, "userType"),
                    jobTitle = GetPropertyValue(root, "jobTitle"),
                    companyName = GetPropertyValue(root, "companyName"),
                    department = GetPropertyValue(root, "department"),
                    officeLocation = GetPropertyValue(root, "officeLocation"),
                    streetAddress = GetPropertyValue(root, "streetAddress"),
                    city = GetPropertyValue(root, "city"),
                    state = GetPropertyValue(root, "state"),
                    postalCode = GetPropertyValue(root, "postalCode"),
                    country = GetPropertyValue(root, "country"),
                    mobilePhone = GetPropertyValue(root, "mobilePhone"),
                    mail = GetPropertyValue(root, "mail")
                };

                // Get businessPhones if it exists
                if (root.TryGetProperty("businessPhones", out var phonesElement) &&
                    phonesElement.ValueKind == JsonValueKind.Array)
                {
                    foreach (var phone in phonesElement.EnumerateArray())
                    {
                        if (phone.ValueKind == JsonValueKind.String)
                            user.businessPhones.Add(phone.GetString());
                    }
                }

                // Get extension property names from environment variables with fallbacks
                var orgExtensionId = Environment.GetEnvironmentVariable("EXTENSION_ID_ORGANIZATION_NAME") ?? "extension_8a2d87f30a864e7e8b70f49083a6ff68_OrganizationName";
                var addressExtensionId = Environment.GetEnvironmentVariable("EXTENSION_ID_ADDRESS") ?? "extension_8a2d87f30a864e7e8b70f49083a6ff68_Address";

                // Debug logging for extension property mapping
                Console.WriteLine($"ActiveUserConverter Debug - OrgExtensionId: '{orgExtensionId}'");
                Console.WriteLine($"ActiveUserConverter Debug - AddressExtensionId: '{addressExtensionId}'");

                // Set the extension properties
                user.OrganizationName = GetPropertyValue(root, orgExtensionId);
                user.Address = GetPropertyValue(root, addressExtensionId);

                // Debug logging for extracted values
                Console.WriteLine($"ActiveUserConverter Debug - OrganizationName: '{user.OrganizationName}'");
                Console.WriteLine($"ActiveUserConverter Debug - Address: '{user.Address}'");

                return user;
            }

            private string GetPropertyValue(JsonElement element, string propertyName)
            {
                return element.TryGetProperty(propertyName, out var property) &&
                       property.ValueKind == JsonValueKind.String ?
                       property.GetString() : null;
            }

            public override void Write(Utf8JsonWriter writer, ActiveUser value, JsonSerializerOptions options)
            {
                writer.WriteStartObject();

                WriteProperty(writer, "id", value.id);
                WriteProperty(writer, "displayName", value.displayName);
                WriteProperty(writer, "givenName", value.givenName);
                WriteProperty(writer, "surname", value.surname);
                WriteProperty(writer, "userType", value.userType);
                WriteProperty(writer, "jobTitle", value.jobTitle);
                WriteProperty(writer, "companyName", value.companyName);
                WriteProperty(writer, "department", value.department);
                WriteProperty(writer, "officeLocation", value.officeLocation);
                WriteProperty(writer, "streetAddress", value.streetAddress);
                WriteProperty(writer, "city", value.city);
                WriteProperty(writer, "state", value.state);
                WriteProperty(writer, "postalCode", value.postalCode);
                WriteProperty(writer, "country", value.country);
                WriteProperty(writer, "mobilePhone", value.mobilePhone);
                WriteProperty(writer, "mail", value.mail);

                // Write businessPhones array
                writer.WritePropertyName("businessPhones");
                writer.WriteStartArray();
                foreach (var phone in value.businessPhones)
                {
                    writer.WriteStringValue(phone);
                }
                writer.WriteEndArray();

                // Get extension property names from environment with fallbacks
                var orgExtensionId = Environment.GetEnvironmentVariable("EXTENSION_ID_ORGANIZATION_NAME") ?? "extension_8a2d87f30a864e7e8b70f49083a6ff68_OrganizationName";
                var addressExtensionId = Environment.GetEnvironmentVariable("EXTENSION_ID_ADDRESS") ?? "extension_8a2d87f30a864e7e8b70f49083a6ff68_Address";

                // Write extension properties with the environment-based property names
                WriteProperty(writer, orgExtensionId, value.OrganizationName);
                WriteProperty(writer, addressExtensionId, value.Address);

                writer.WriteEndObject();
            }

            private void WriteProperty(Utf8JsonWriter writer, string propertyName, string value)
            {
                if (value != null)
                {
                    writer.WritePropertyName(propertyName);
                    writer.WriteStringValue(value);
                }
            }
        }
    

}

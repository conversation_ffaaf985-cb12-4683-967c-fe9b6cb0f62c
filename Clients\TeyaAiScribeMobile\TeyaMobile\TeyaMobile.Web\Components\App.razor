<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <HeadOutlet @rendermode="InteractiveServer" />
    <link rel="stylesheet" href="_content/TeyaMobile.Shared/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="_content/TeyaMobile.Shared/app.css" />
    <link rel="icon" type="image/png" href="_content/TeyaMobile.Shared/favicon.png" />
    <link href="TeyaMobile.styles.css" rel="stylesheet" />
    <link href="_content/Syncfusion.Blazor.Themes/bootstrap5.css" rel="stylesheet" />
</head>

<body>
    <Routes @rendermode="InteractiveServer" />

    <!-- Reconnection Modal -->
    <component type="typeof(ReconnectModal)" render-mode="InteractiveServer" />

    <!-- App Settings Initializer Component -->
    <component type="typeof(AppSettingsInitializer)" render-mode="InteractiveServer" />

    <!-- Scripts -->
    <script src="js/AudioRecorder.js"></script>
    <script src="_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js" type="text/javascript"></script>
    <script src="js/blazor-reconnect.js"></script>
    <script src="_framework/blazor.web.js"></script>

</body>

</html>
